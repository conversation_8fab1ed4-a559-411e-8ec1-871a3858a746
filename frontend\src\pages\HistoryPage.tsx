import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth'; // Corrected import path
import { useChat } from '../hooks/useChat'; // Corrected import path
import TitleEditor from '../components/Chat/TitleEditor';

// Define the structure for the thread summary expected from the backend
// Based on backend/app/models/chat.py::ThreadSummary
interface ThreadSummary {
  thread_id: number;
  last_updated: string; // ISO string date
  preview?: string | null;
  titulo?: string | null; // Thread title from metadata
}

const HistoryPage: React.FC = () => {
  const { session } = useAuth();
  const { setCurrentThreadId, clearMessages } = useChat(); // Get context functions
  const navigate = useNavigate();
  const [threads, setThreads] = useState<ThreadSummary[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchThreads = async () => {
      if (!session?.access_token) {
        setError("Not authenticated.");
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        // Construct full URL using environment variable
        const threadsUrl = `${import.meta.env.VITE_API_BASE_URL}/threads/`;
        console.log("Fetching history from:", threadsUrl);
        const response = await fetch(threadsUrl, { // Fetch thread list
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          throw new Error(errorData.detail || 'Failed to fetch history');
        }

        const data: ThreadSummary[] = await response.json();
        // Sort by last_updated descending (API might already do this, but good practice)
        data.sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime());
        setThreads(data);

      } catch (err) {
        console.error("Error fetching history:", err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchThreads();
  }, [session]);

  // Function to handle loading a selected thread (Task 13.5)
  const handleLoadThread = (threadId: number) => {
    console.log(`Loading thread: ${threadId}`);
    clearMessages(); // Clear existing messages from context
    setCurrentThreadId(threadId); // Set the selected thread ID in context
    navigate('/chat'); // Navigate back to the chat page
  };

  // Function to update thread title from history
  const handleUpdateTitle = async (threadId: number, newTitle: string) => {
    if (!session?.access_token) return;

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/threads/${threadId}/metadata`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({ titulo: newTitle })
      });

      if (response.ok) {
        // Update the local threads state
        setThreads(prevThreads =>
          prevThreads.map(thread =>
            thread.thread_id === threadId
              ? { ...thread, titulo: newTitle }
              : thread
          )
        );
      } else {
        console.error('Failed to update title:', response.statusText);
        throw new Error(`Failed to update title: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error updating thread title:', error);
      throw error;
    }
  };

  // Helper to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, { dateStyle: 'medium', timeStyle: 'short' });
  };

  return (
    <div className="h-full overflow-y-auto p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-4 sm:space-y-0">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800">Conversation History</h1>
          <Link
            to="/chat"
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm transition-colors self-start sm:self-auto"
          >
            Back to Chat
          </Link>
        </div>

      {isLoading && <p className="text-center text-gray-500">Loading history...</p>}
      {error && <p className="text-center text-red-600">Error: {error}</p>}

      {!isLoading && !error && threads.length === 0 && (
        <p className="text-center text-gray-500">No conversation history found.</p>
      )}

      {!isLoading && !error && threads.length > 0 && (
        <div className="space-y-4">
          {threads.map((thread) => (
            <div
              key={thread.thread_id}
              className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1 min-w-0 mr-4">
                  <TitleEditor
                    title={thread.titulo || `Chat ${thread.thread_id}`}
                    onSave={(newTitle) => handleUpdateTitle(thread.thread_id, newTitle)}
                    className="mb-1"
                    placeholder={`Chat ${thread.thread_id}`}
                  />
                </div>
                <p className="text-xs text-gray-500 flex-shrink-0">{formatDate(thread.last_updated)}</p>
              </div>
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600 truncate italic flex-1">
                  {thread.preview || 'No preview available'}
                </p>
                <button
                  onClick={() => handleLoadThread(thread.thread_id)}
                  className="ml-4 px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors flex-shrink-0"
                >
                  Abrir
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
      </div>
    </div>
  );
};

export default HistoryPage;