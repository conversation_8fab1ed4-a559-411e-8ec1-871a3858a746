import logging
import json # Import json
from uuid import UUID
from typing import List, Dict, Optional, Set # Import Set
from datetime import datetime

from fastapi import HTTPException, status
from supabase import AsyncClient

from app.core.database import get_supabase_client
from app.models.chat import ThreadSummary, ChatMessage

# Use explicit logger name to match configuration in main.py
logger = logging.getLogger("app.services.thread_service")

async def get_threads_for_user(user_id: UUID) -> List[ThreadSummary]:
    """
    Fetches a summary of all chat threads for a given user, ordered by
    the most recent activity.

    Args:
        user_id: The UUID of the user whose threads are to be fetched.

    Returns:
        A list of ThreadSummary objects, ordered by last update time descending.

    Raises:
        HTTPException: If a database error occurs (500).
    """
    try:
        supabase: AsyncClient = await get_supabase_client()
        logger.info(f"Fetching thread summaries for user_id: {user_id}")

        # Fetch all relevant message fields for the user, ordered by creation time descending
        # This allows us to easily pick the latest message for each thread
        # Execute synchronously
        response = supabase.table("threads") \
            .select("thread_id, created_at, content") \
            .eq("user_id", user_id) \
            .order("created_at", desc=True) \
            .execute()

        if not response.data:
            logger.info(f"No threads found for user_id: {user_id}")
            return []

        # Process messages to get unique threads and their latest info
        threads_summary_map: Dict[int, ThreadSummary] = {}
        for message_data in response.data:
            thread_id = message_data.get("thread_id")
            created_at = message_data.get("created_at")
            content = message_data.get("content")

            if thread_id is None or created_at is None:
                logger.warning(f"Skipping message data due to missing thread_id or created_at: {message_data}")
                continue

            # Since messages are ordered descending, the first time we see a thread_id,
            # it corresponds to the latest message in that thread.
            if thread_id not in threads_summary_map:
                try:
                    # Ensure created_at is a datetime object
                    last_updated_dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))

                    threads_summary_map[thread_id] = ThreadSummary(
                        thread_id=thread_id,
                        last_updated=last_updated_dt,
                        preview=content[:100] + "..." if content and len(content) > 100 else content, # Simple preview
                        titulo=None  # Will be populated below
                    )
                except Exception as e:
                     logger.warning(f"Failed to process message for thread {thread_id}. Data: {message_data}. Error: {e}")

        # Fetch metadata (titles) for all threads
        if threads_summary_map:
            thread_ids = list(threads_summary_map.keys())
            try:
                metadata_response = supabase.table("threads_metadata") \
                    .select("thread_id, titulo") \
                    .in_("thread_id", thread_ids) \
                    .execute()

                # Update thread summaries with titles
                if metadata_response.data:
                    for metadata in metadata_response.data:
                        thread_id = metadata.get("thread_id")
                        titulo = metadata.get("titulo")
                        if thread_id in threads_summary_map:
                            threads_summary_map[thread_id].titulo = titulo

                logger.info(f"Fetched metadata for {len(metadata_response.data) if metadata_response.data else 0} threads")
            except Exception as e:
                logger.warning(f"Failed to fetch thread metadata for user {user_id}: {e}")
                # Continue without titles if metadata fetch fails

        # Convert map values to a list and return (already sorted by virtue of processing order)
        summary_list = list(threads_summary_map.values())
        logger.info(f"Successfully fetched {len(summary_list)} thread summaries for user_id: {user_id}")
        return summary_list

    except Exception as e:
        logger.exception(f"Database error while fetching threads for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while fetching chat threads: {e}",
        )

# --- Placeholder functions for subsequent tasks ---

async def check_thread_authorization(thread_id: int, user_id: UUID) -> bool:
    """
    Checks if the given user is authorized to access the specified thread
    by verifying if they have at least one message in that thread.
    """
    logger.info(f"Checking authorization for thread {thread_id}, user {user_id}")
    try:
        supabase: AsyncClient = await get_supabase_client()
        # Use count='exact' for efficiency, only need to know if at least one exists
        # Execute synchronously
        response = supabase.table("threads") \
            .select("user_id", count="exact") \
            .eq("thread_id", thread_id) \
            .eq("user_id", user_id) \
            .limit(1) \
            .execute()

        is_authorized = response.count > 0
        logger.info(f"Authorization result for thread {thread_id}, user {user_id}: {is_authorized}")
        return is_authorized
    except Exception as e:
        logger.exception(f"Database error checking thread authorization for thread {thread_id}, user {user_id}: {e}")
        # Default to unauthorized on error for security
        return False


async def get_messages_for_thread(
    thread_id: int,
    limit: int = 50,
    offset: int = 0,
    load_recent: bool = True
) -> List[ChatMessage]:
    """
    Fetches messages for a specific thread with pagination support.
    Injects tool names for tool_use messages.

    Args:
        thread_id: The thread to fetch messages from
        limit: Maximum number of messages to return (default: 50)
        offset: Number of messages to skip (default: 0)
        load_recent: If True, loads most recent messages first (default: True)
    """
    logger.info(f"Fetching messages for thread_id: {thread_id} (limit={limit}, offset={offset}, load_recent={load_recent})")
    try:
        supabase: AsyncClient = await get_supabase_client()

        # Build query with pagination
        query = supabase.table("threads") \
            .select("*") \
            .eq("thread_id", thread_id)

        # Order by creation time (recent first if load_recent=True)
        if load_recent:
            query = query.order("created_at", desc=True)
        else:
            query = query.order("created_at", desc=False)

        # Apply pagination
        query = query.range(offset, offset + limit - 1)

        response = query.execute()

        if response.data:
            messages_data = response.data # Original data from DB
            tool_ids_to_fetch: Set[UUID] = set()

            # First pass: Extract tool_ids from tool_use messages
            print(f"DEBUG: Starting first pass to extract tool_ids for thread {thread_id}")
            for msg_data in messages_data:
                if msg_data.get("type") == "tool_use" and msg_data.get("content"):
                    try:
                        content_json = json.loads(msg_data["content"])
                        tool_input = content_json.get("toolInput", {})

                        # Prioritize checking tool_id directly under toolInput
                        tool_id_str = tool_input.get("tool_id")

                        # If not found directly, try parsing tool_params (if it's a string)
                        if not tool_id_str:
                            tool_params_str = tool_input.get("tool_params")
                            if isinstance(tool_params_str, str):
                                try:
                                    tool_params_json = json.loads(tool_params_str)
                                    tool_id_str = tool_params_json.get("tool_id")
                                except json.JSONDecodeError:
                                    logger.warning(f"Failed to parse tool_params JSON string in message {msg_data.get('id')}: {tool_params_str}")

                        if tool_id_str:
                            try:
                                tool_uuid = UUID(tool_id_str)
                                tool_ids_to_fetch.add(tool_uuid)
                                print(f"DEBUG: Extracted tool_id {tool_uuid} from message {msg_data.get('id')}")
                            except ValueError:
                                logger.warning(f"Invalid UUID format for tool_id '{tool_id_str}' in message {msg_data.get('id')}")
                        # else: # Optional: Log if no tool_id found after checking both places
                        #     logger.warning(f"Could not find tool_id in message {msg_data.get('id')}")

                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse content JSON for tool_use message {msg_data.get('id')}")
                    except Exception as e:
                         logger.warning(f"Error processing tool_use message {msg_data.get('id')}: {e}")


            # Fetch tool names if any IDs were found
            tool_name_map: Dict[str, str] = {}
            if tool_ids_to_fetch:
                tool_ids_str_list = [str(uid) for uid in tool_ids_to_fetch]
                print(f"INFO: Attempting to fetch names for tool_ids: {tool_ids_str_list}") # Use print for visibility
                try:
                    tools_response = supabase.table("tools") \
                        .select("id, tool_name") \
                        .in_("id", tool_ids_str_list) \
                        .execute()

                    print(f"DEBUG: Raw response from tools table query: {tools_response.data}")

                    if tools_response.data:
                        for tool_data in tools_response.data:
                            tool_name_map[str(tool_data['id'])] = tool_data['tool_name']
                        print(f"INFO: Successfully fetched names for {len(tool_name_map)} tools.") # Use print for visibility
                    else:
                         logger.warning(f"No tool names found for provided IDs: {tool_ids_str_list}")

                except Exception as e:
                    logger.exception(f"Database error fetching tool names for thread {thread_id}: {e}")
                    # Continue without tool names if fetch fails
                print(f"DEBUG: Final tool_name_map: {tool_name_map}")

            # Second pass: Create validated messages list, injecting tool names
            print(f"DEBUG: Starting second pass to validate messages and inject tool names for thread {thread_id}")
            validated_messages: List[ChatMessage] = []
            for msg_data in messages_data:
                # Create a copy to avoid modifying the original dict used in multiple loops if errors occur
                current_msg_data = msg_data.copy()
                try:
                    # Inject tool_name if applicable (currently only for tool_use)
                    if current_msg_data.get("type") == "tool_use" and current_msg_data.get("content"):
                        tool_name_injected = False # Flag to check if name was injected
                        try:
                            content_json = json.loads(current_msg_data["content"])
                            tool_input = content_json.get("toolInput", {})
                            tool_id_str = tool_input.get("tool_id") # Check direct first

                            if not tool_id_str: # If not direct, check inside params string
                                tool_params_str = tool_input.get("tool_params")
                                if isinstance(tool_params_str, str):
                                    try:
                                        tool_params_json = json.loads(tool_params_str)
                                        tool_id_str = tool_params_json.get("tool_id")
                                    except json.JSONDecodeError:
                                        pass # Ignore error here, already logged

                            # Use str(UUID(tool_id_str)) to ensure consistent key format with tool_name_map
                            if tool_id_str:
                                tool_uuid_str_key = str(UUID(tool_id_str)) # Validate and convert to string key
                                if tool_uuid_str_key in tool_name_map:
                                    current_msg_data['tool_name'] = tool_name_map[tool_uuid_str_key]
                                    print(f"DEBUG: Injected tool_name '{current_msg_data['tool_name']}' for message {current_msg_data.get('id')}")
                                    tool_name_injected = True
                                elif tool_ids_to_fetch: # Only log if we expected to find it
                                     logger.warning(f"Tool ID {tool_uuid_str_key} found in message {current_msg_data.get('id')} but its name was not found in the fetched map.")
                        except (json.JSONDecodeError, ValueError, TypeError) as parse_err:
                             logger.warning(f"Could not parse/validate tool_id or find name for tool_use message {current_msg_data.get('id')} during injection: {parse_err}")
                        except Exception as inject_err:
                             logger.warning(f"Unexpected error injecting tool_name for message {current_msg_data.get('id')}: {inject_err}")

                    # Validate the potentially modified data
                    validated_messages.append(ChatMessage.model_validate(current_msg_data))
                except Exception as e:
                    logger.warning(f"Failed to validate message data for thread {thread_id}. Data: {current_msg_data}. Error: {e}")
                    # Skipping invalid messages for now
            # If we loaded recent messages first, reverse to maintain chronological order
            if load_recent and validated_messages:
                validated_messages.reverse()

            logger.info(f"Successfully fetched and validated {len(validated_messages)} messages for thread {thread_id}")
            return validated_messages
        else:
            logger.info(f"No messages found for thread {thread_id}")
            return []
    except Exception as e:
        logger.exception(f"Database error fetching messages for thread {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while fetching messages for thread {thread_id}: {e}",
        )


async def get_thread_message_count(thread_id: int) -> int:
    """
    Get the total number of messages in a specific thread.
    Optimized for performance using count query.
    """
    logger.info(f"Getting message count for thread_id: {thread_id}")
    try:
        supabase: AsyncClient = await get_supabase_client()

        # Use count query for efficiency
        response = supabase.table("threads") \
            .select("*", count="exact") \
            .eq("thread_id", thread_id) \
            .execute()

        count = response.count if response.count is not None else 0
        logger.info(f"Thread {thread_id} has {count} messages")
        return count

    except Exception as e:
        logger.exception(f"Database error getting message count for thread {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while getting message count for thread {thread_id}: {e}",
        )