import logging
from typing import Optional
from datetime import datetime, timezone
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status

from app.core.database import get_supabase_client
from app.models.chat import ThreadMetadata, ThreadMetadataCreate, ThreadMetadataUpdate
from supabase import AsyncClient

logger = logging.getLogger(__name__)


async def create_thread_metadata(metadata_in: ThreadMetadataCreate) -> ThreadMetadata:
    """
    Create metadata for a thread if it doesn't already exist.
    
    Args:
        metadata_in: ThreadMetadataCreate object with thread_id and titulo
        
    Returns:
        ThreadMetadata object
        
    Raises:
        HTTPException: If a database error occurs
    """
    try:
        supabase: AsyncClient = await get_supabase_client()
        logger.info(f"Creating metadata for thread_id: {metadata_in.thread_id}")
        
        # Check if metadata already exists
        existing_response = await supabase.table("threads_metadata") \
            .select("*") \
            .eq("thread_id", metadata_in.thread_id) \
            .execute()
            
        if existing_response.data:
            logger.info(f"Metadata already exists for thread_id: {metadata_in.thread_id}")
            # Return existing metadata
            existing_data = existing_response.data[0]
            return ThreadMetadata(
                thread_id=existing_data["thread_id"],
                titulo=existing_data["titulo"],
                created_at=datetime.fromisoformat(existing_data["created_at"].replace("Z", "+00:00")),
                updated_at=datetime.fromisoformat(existing_data["updated_at"].replace("Z", "+00:00"))
            )
        
        # Create new metadata
        now = datetime.now(timezone.utc)
        metadata_data = {
            "thread_id": metadata_in.thread_id,
            "titulo": metadata_in.titulo,
            "created_at": now.isoformat(),
            "updated_at": now.isoformat()
        }
        
        response = await supabase.table("threads_metadata").insert(metadata_data).execute()
        
        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create thread metadata"
            )
            
        created_data = response.data[0]
        logger.info(f"Successfully created metadata for thread_id: {metadata_in.thread_id}")
        
        return ThreadMetadata(
            thread_id=created_data["thread_id"],
            titulo=created_data["titulo"],
            created_at=datetime.fromisoformat(created_data["created_at"].replace("Z", "+00:00")),
            updated_at=datetime.fromisoformat(created_data["updated_at"].replace("Z", "+00:00"))
        )
        
    except Exception as e:
        logger.exception(f"Error creating thread metadata for thread_id {metadata_in.thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error creating thread metadata: {str(e)}"
        )


async def get_thread_metadata(thread_id: int) -> Optional[ThreadMetadata]:
    """
    Get metadata for a specific thread.
    
    Args:
        thread_id: The thread ID to get metadata for
        
    Returns:
        ThreadMetadata object if found, None otherwise
        
    Raises:
        HTTPException: If a database error occurs
    """
    try:
        supabase: AsyncClient = await get_supabase_client()
        logger.info(f"Getting metadata for thread_id: {thread_id}")
        
        response = await supabase.table("threads_metadata") \
            .select("*") \
            .eq("thread_id", thread_id) \
            .execute()
            
        if not response.data:
            logger.info(f"No metadata found for thread_id: {thread_id}")
            return None
            
        data = response.data[0]
        return ThreadMetadata(
            thread_id=data["thread_id"],
            titulo=data["titulo"],
            created_at=datetime.fromisoformat(data["created_at"].replace("Z", "+00:00")),
            updated_at=datetime.fromisoformat(data["updated_at"].replace("Z", "+00:00"))
        )
        
    except Exception as e:
        logger.exception(f"Error getting thread metadata for thread_id {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error getting thread metadata: {str(e)}"
        )


async def update_thread_metadata(thread_id: int, metadata_update: ThreadMetadataUpdate) -> ThreadMetadata:
    """
    Update metadata for a specific thread.
    
    Args:
        thread_id: The thread ID to update metadata for
        metadata_update: ThreadMetadataUpdate object with new titulo
        
    Returns:
        Updated ThreadMetadata object
        
    Raises:
        HTTPException: If thread metadata not found or database error occurs
    """
    try:
        supabase: AsyncClient = await get_supabase_client()
        logger.info(f"Updating metadata for thread_id: {thread_id}")
        
        # Check if metadata exists
        existing_response = await supabase.table("threads_metadata") \
            .select("*") \
            .eq("thread_id", thread_id) \
            .execute()
            
        if not existing_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread metadata not found for thread_id: {thread_id}"
            )
        
        # Update metadata
        now = datetime.now(timezone.utc)
        update_data = {
            "titulo": metadata_update.titulo,
            "updated_at": now.isoformat()
        }
        
        response = await supabase.table("threads_metadata") \
            .update(update_data) \
            .eq("thread_id", thread_id) \
            .execute()
            
        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update thread metadata"
            )
            
        updated_data = response.data[0]
        logger.info(f"Successfully updated metadata for thread_id: {thread_id}")
        
        return ThreadMetadata(
            thread_id=updated_data["thread_id"],
            titulo=updated_data["titulo"],
            created_at=datetime.fromisoformat(updated_data["created_at"].replace("Z", "+00:00")),
            updated_at=datetime.fromisoformat(updated_data["updated_at"].replace("Z", "+00:00"))
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error updating thread metadata for thread_id {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error updating thread metadata: {str(e)}"
        )


async def ensure_thread_metadata_exists(thread_id: int) -> ThreadMetadata:
    """
    Ensure that metadata exists for a thread, creating it if necessary.
    
    Args:
        thread_id: The thread ID to ensure metadata exists for
        
    Returns:
        ThreadMetadata object (existing or newly created)
    """
    existing_metadata = await get_thread_metadata(thread_id)
    if existing_metadata:
        return existing_metadata
    
    # Create new metadata with default title
    metadata_create = ThreadMetadataCreate(thread_id=thread_id, titulo="Chat sin título")
    return await create_thread_metadata(metadata_create)
